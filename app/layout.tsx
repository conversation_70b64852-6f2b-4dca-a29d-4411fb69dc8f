import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import Header from '@/components/Header'
import Footer from '@/components/Footer'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'VEE Photo Studio - Profesyonel Fotoğraf Çekimleri',
  description: 'Model çekimleri, e-ticaret ürün fotoğrafları ve moda çekimleri için profesyonel fotoğraf stüdyosu. Kreatif ve kaliteli çözümler.',
  keywords: 'fotoğraf stüdyosu, model çekimi, e-ticaret fotoğraf, moda çekimi, ürün fotoğrafı',
  authors: [{ name: 'VEE Photo Studio' }],
  openGraph: {
    title: 'VEE Photo Studio - Profesyonel Fotoğraf Çekimleri',
    description: 'Model çekimleri, e-ticaret ürün fotoğrafları ve moda çekimleri için profesyonel fotoğraf stüdyosu.',
    type: 'website',
    locale: 'tr_TR',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="tr">
      <body className={inter.className}>
        <Header />
        <main className="min-h-screen">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  )
}
