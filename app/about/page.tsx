'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'

export default function About() {
  const teamMembers = [
    {
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      role: 'Yö<PERSON><PERSON>',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
      description: 'Kreatif vizyonu ve liderlik becerileri ile ekibi yönlendiren deneyimli yönetmen.'
    },
    {
      name: '<PERSON><PERSON>',
      role: '<PERSON><PERSON><PERSON>',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
      description: 'Proje yönetimi ve müşteri ilişkilerinde uzman, operasyonel süreçlerin sorumlusu.'
    },
    {
      name: 'Ömer Irmak',
      role: 'Video Uzmanı',
      image: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
      description: 'Video prodüksiyon ve sinematografi alanında uzman, hareket halindeki anları yakalıyor.'
    },
    {
      name: 'Orçun Öztürk',
      role: 'Photographer / Art Director',
      image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      description: 'Fotoğrafçılık ve sanat yönetimi konularında uzman, yaratıcı konseptlerin mimarı.'
    }
  ]

  const values = [
    {
      title: 'Kalite',
      description: 'Her projede en yüksek kalite standartlarını hedefliyoruz.',
      icon: '⭐'
    },
    {
      title: 'Yaratıcılık',
      description: 'Benzersiz ve yaratıcı çözümlerle fark yaratıyoruz.',
      icon: '🎨'
    },
    {
      title: 'Profesyonellik',
      description: 'Zamanında teslimat ve profesyonel hizmet anlayışı.',
      icon: '💼'
    },
    {
      title: 'Müşteri Memnuniyeti',
      description: 'Müşteri memnuniyeti bizim için en önemli öncelik.',
      icon: '❤️'
    }
  ]

  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 to-purple-900 text-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              Hakkımızda
            </h1>
            <p className="text-xl md:text-2xl text-gray-200 leading-relaxed">
              2018 yılından beri profesyonel fotoğraf çekimleri ile markaların hikayelerini anlatıyoruz.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-4xl font-bold mb-6 gradient-text">
                Hikayemiz
              </h2>
              <div className="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  VEE Studio, 2018 yılında fotoğraf tutkusu olan bir grup arkadaş tarafından kuruldu. 
                  Amacımız, her markanın kendine özgü hikayesini en güzel şekilde görsel olarak anlatmaktı.
                </p>
                <p>
                  Yıllar içinde büyüyen ekibimiz ve gelişen teknolojimizle, model çekimlerinden 
                  e-ticaret ürün fotoğraflarına kadar geniş bir yelpazede hizmet vermeye başladık.
                </p>
                <p>
                  Bugün, 500'den fazla mutlu müşterimiz ve 1000'den fazla tamamlanmış projemizle, 
                  sektörde güvenilir bir isim haline geldik.
                </p>
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <Image
                src="https://images.unsplash.com/photo-1556761175-b413da4baf72?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2074&q=80"
                alt="Studio"
                width={600}
                height={400}
                className="rounded-2xl shadow-2xl"
              />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold mb-6 gradient-text">
              Değerlerimiz
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Çalışma prensiplerimizdeki temel değerler, her projede kaliteli sonuçlar elde etmemizi sağlar.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center p-6 bg-white rounded-xl shadow-lg hover-lift"
              >
                <div className="text-4xl mb-4">{value.icon}</div>
                <h3 className="text-xl font-bold mb-3 text-gray-800">{value.title}</h3>
                <p className="text-gray-600">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold mb-6 gradient-text">
              Ekibimiz
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Deneyimli ve tutkulu ekibimizle, her projede mükemmelliği hedefliyoruz.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {teamMembers.map((member, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="text-center group"
              >
                <div className="relative mb-6 mx-auto w-48 h-48 overflow-hidden rounded-full">
                  <Image
                    src={member.image}
                    alt={member.name}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-110"
                  />
                </div>
                <h3 className="text-2xl font-bold mb-2 text-gray-800">{member.name}</h3>
                <p className="text-primary-600 font-semibold mb-3">{member.role}</p>
                <p className="text-gray-600">{member.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}
