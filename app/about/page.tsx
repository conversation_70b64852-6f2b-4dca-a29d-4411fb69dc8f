'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'

export default function About() {


  const values = [
    {
      title: '<PERSON><PERSON>',
      description: 'Her projede en yüksek kalite standartlarını hedefliyoruz.',
      icon: '⭐'
    },
    {
      title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      description: 'Benzersiz ve yaratıcı çözümlerle fark yaratıyoruz.',
      icon: '🎨'
    },
    {
      title: 'Profesyonellik',
      description: 'Zamanında teslimat ve profesyonel hizmet anlayışı.',
      icon: '💼'
    },
    {
      title: '<PERSON><PERSON><PERSON><PERSON>i Memnuniyeti',
      description: 'Müşteri memnuniyeti bizim için en önemli öncelik.',
      icon: '❤️'
    }
  ]

  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 to-purple-900 text-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              Hakkımızda
            </h1>
            <p className="text-xl md:text-2xl text-gray-200 leading-relaxed">
              2018 yılından beri profesyonel fotoğraf çekimleri ile markaların hikayelerini anlatıyoruz.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-4xl font-bold mb-6 gradient-text">
                Hikayemiz
              </h2>
              <div className="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  VEE Studio, 2015 yılında fotoğraf tutkusu olan bir grup arkadaş tarafından kuruldu.
                  Amacımız, her markanın kendine özgü hikayesini en güzel şekilde görsel olarak anlatmaktı.
                </p>
                <p>
                  Yıllar içinde büyüyen ekibimiz ve gelişen teknolojimizle, model çekimlerinden
                  e-ticaret ürün fotoğraflarına kadar geniş bir yelpazede hizmet vermeye başladık.
                </p>
                <p>
                  Bugün, 500'den fazla mutlu müşterimiz ve 1000'den fazla tamamlanmış projemizle,
                  sektörde güvenilir bir isim haline geldik.
                </p>
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <Image
                src="https://images.unsplash.com/photo-1556761175-b413da4baf72?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2074&q=80"
                alt="Studio"
                width={600}
                height={400}
                className="rounded-2xl shadow-2xl"
              />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold mb-6 gradient-text">
              Değerlerimiz
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Çalışma prensiplerimizdeki temel değerler, her projede kaliteli sonuçlar elde etmemizi sağlar.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center p-6 bg-white rounded-xl shadow-lg hover-lift"
              >
                <div className="text-4xl mb-4">{value.icon}</div>
                <h3 className="text-xl font-bold mb-3 text-gray-800">{value.title}</h3>
                <p className="text-gray-600">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>


    </div>
  )
}
