'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'

export default function Services() {
  const services = [
    {
      title: 'Model Çekimleri',
      subtitle: 'Profesyonel Portre & Model Fotoğrafları',
      description: 'Kişisel portföy çekimlerinden profesyonel model fotoğraflarına kadar geniş bir yelpazede hizmet veriyoruz.',
      image: 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
      features: [
        'Kişisel Portföy Çekimleri',
        'Profesyonel Model Fotoğrafları',
        'Headshot & Business Portreleri',
        'Yaratıcı Portre Çekimleri',
        'Sosyal Medya İçerikleri'
      ],
      pricing: '₺1.500 - ₺5.000',
      duration: '2-4 saat',
      color: 'from-pink-500 to-rose-500'
    },
    {
      title: 'E-ticaret Ürün Çekimleri',
      subtitle: 'Satışlarınızı Artıran Ürün Fotoğrafları',
      description: 'Ürünlerinizi en çekici şekilde sergileyen, satışlarınızı artıran profesyonel e-ticaret fotoğrafları.',
      image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
      features: [
        'Beyaz Zemin Ürün Fotoğrafları',
        'Lifestyle Ürün Çekimleri',
        'Detay ve Makro Fotoğraflar',
        '360° Ürün Görüntüleri',
        'Katalog Fotoğrafları'
      ],
      pricing: '₺100 - ₺500 / ürün',
      duration: '1-2 saat',
      color: 'from-purple-500 to-indigo-500'
    },
    {
      title: 'Moda & Lifestyle Çekimleri',
      subtitle: 'Yaratıcı Moda ve Yaşam Tarzı Fotoğrafları',
      description: 'Markanızın kimliğini yansıtan, etkileyici moda ve lifestyle fotoğrafları ile öne çıkın.',
      image: 'https://images.unsplash.com/photo-1445205170230-053b83016050?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80',
      features: [
        'Moda Editorial Çekimleri',
        'Lifestyle Fotoğrafları',
        'Marka Kampanya Çekimleri',
        'Lookbook Fotoğrafları',
        'Sosyal Medya Kampanyaları'
      ],
      pricing: '₺3.000 - ₺10.000',
      duration: '4-8 saat',
      color: 'from-cyan-500 to-blue-500'
    },
    {
      title: 'Kurumsal Çekimler',
      subtitle: 'Profesyonel Kurumsal Fotoğraf Hizmetleri',
      description: 'Şirketinizin profesyonel imajını güçlendiren kurumsal fotoğraf çekimleri.',
      image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
      features: [
        'Kurumsal Portre Çekimleri',
        'Ofis ve Mekan Fotoğrafları',
        'Etkinlik Fotoğrafları',
        'Ürün Lansmanları',
        'Kurumsal Katalog Çekimleri'
      ],
      pricing: '₺2.000 - ₺8.000',
      duration: '3-6 saat',
      color: 'from-emerald-500 to-teal-500'
    }
  ]

  const process = [
    {
      step: '01',
      title: 'Konsültasyon',
      description: 'İhtiyaçlarınızı dinler, projenizi planlıyoruz.'
    },
    {
      step: '02',
      title: 'Planlama',
      description: 'Çekim konseptini ve detayları belirliyoruz.'
    },
    {
      step: '03',
      title: 'Çekim',
      description: 'Profesyonel ekipmanlarla çekimi gerçekleştiriyoruz.'
    },
    {
      step: '04',
      title: 'Düzenleme',
      description: 'Fotoğrafları profesyonelce düzenleyip teslim ediyoruz.'
    }
  ]

  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 to-purple-900 text-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              Hizmetlerimiz
            </h1>
            <p className="text-xl md:text-2xl text-gray-200 leading-relaxed">
              Profesyonel fotoğraf çekimi ihtiyaçlarınız için kapsamlı çözümler sunuyoruz.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="space-y-20">
            {services.map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${
                  index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
                }`}
              >
                {/* Image */}
                <div className={`relative ${index % 2 === 1 ? 'lg:col-start-2' : ''}`}>
                  <div className="relative h-96 rounded-2xl overflow-hidden shadow-2xl">
                    <Image
                      src={service.image}
                      alt={service.title}
                      fill
                      className="object-cover"
                    />
                    <div className={`absolute inset-0 bg-gradient-to-t ${service.color} opacity-20`}></div>
                  </div>
                </div>

                {/* Content */}
                <div className={index % 2 === 1 ? 'lg:col-start-1 lg:row-start-1' : ''}>
                  <div className="space-y-6">
                    <div>
                      <h2 className="text-4xl font-bold mb-2 text-gray-800">
                        {service.title}
                      </h2>
                      <p className="text-xl text-gray-600 mb-4">{service.subtitle}</p>
                      <p className="text-gray-600 leading-relaxed">{service.description}</p>
                    </div>

                    {/* Features */}
                    <div>
                      <h3 className="text-xl font-semibold mb-4 text-gray-800">Hizmet Kapsamı:</h3>
                      <ul className="space-y-2">
                        {service.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-center text-gray-700">
                            <div className={`w-2 h-2 rounded-full bg-gradient-to-r ${service.color} mr-3`}></div>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Pricing & Duration */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-semibold text-gray-800 mb-1">Fiyat Aralığı</h4>
                        <p className="text-gray-600">{service.pricing}</p>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-semibold text-gray-800 mb-1">Süre</h4>
                        <p className="text-gray-600">{service.duration}</p>
                      </div>
                    </div>

                    {/* CTA Button */}
                    <Link
                      href="/contact"
                      className={`inline-flex items-center px-8 py-4 rounded-full bg-gradient-to-r ${service.color} text-white font-semibold hover:shadow-xl transition-all duration-300 hover:scale-105`}
                    >
                      Teklif Al
                      <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                      </svg>
                    </Link>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold mb-6 gradient-text">
              Çalışma Sürecimiz
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Profesyonel ve organize bir süreçle projelerinizi hayata geçiriyoruz.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {process.map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="relative mb-6">
                  <div className="w-20 h-20 bg-gradient-to-r from-primary-500 to-purple-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto">
                    {item.step}
                  </div>
                  {index < process.length - 1 && (
                    <div className="hidden lg:block absolute top-10 left-full w-full h-0.5 bg-gradient-to-r from-primary-500 to-purple-600 opacity-30"></div>
                  )}
                </div>
                <h3 className="text-xl font-bold mb-3 text-gray-800">{item.title}</h3>
                <p className="text-gray-600">{item.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary-500 to-purple-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold mb-6">
              Projenizi Başlatalım
            </h2>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
              Hayalinizdeki fotoğraf çekimi için bizimle iletişime geçin. 
              Ücretsiz konsültasyon ve detaylı teklif alın.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-white text-primary-600 px-8 py-4 rounded-full font-semibold hover:shadow-xl transition-all duration-300 hover:scale-105"
              >
                Ücretsiz Teklif Al
              </Link>
              <Link
                href="/gallery"
                className="border-2 border-white text-white px-8 py-4 rounded-full font-semibold hover:bg-white hover:text-primary-600 transition-all duration-300"
              >
                Çalışmalarımızı İncele
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
