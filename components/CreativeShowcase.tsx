'use client'

import { motion, useScroll, useTransform } from 'framer-motion'
import Image from 'next/image'
import { useRef } from 'react'

const CreativeShowcase = () => {
  const containerRef = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  })

  const y1 = useTransform(scrollYProgress, [0, 1], [0, -100])
  const y2 = useTransform(scrollYProgress, [0, 1], [0, 100])
  const rotate = useTransform(scrollYProgress, [0, 1], [0, 360])

  const showcaseItems = [
    {
      id: 1,
      image: 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      title: 'Portre Sanatı',
      category: 'Model',
      size: 'large'
    },
    {
      id: 2,
      image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      title: '<PERSON><PERSON><PERSON><PERSON>i',
      category: 'Product',
      size: 'medium'
    },
    {
      id: 3,
      image: 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      title: 'Moda Hikayesi',
      category: 'Fashion',
      size: 'medium'
    },
    {
      id: 4,
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      title: 'İş Dünyası',
      category: 'Corporate',
      size: 'small'
    }
  ]

  return (
    <section ref={containerRef} className="py-32 bg-black text-white relative overflow-hidden">
      {/* Animated Background Elements */}
      <motion.div
        style={{ y: y1, rotate }}
        className="absolute top-20 left-10 w-32 h-32 border-2 border-red-500/30 rounded-full"
      />
      <motion.div
        style={{ y: y2 }}
        className="absolute bottom-20 right-10 w-24 h-24 bg-red-600/20 rounded-full blur-xl"
      />
      <motion.div
        style={{ y: y1 }}
        className="absolute top-1/2 left-1/4 w-16 h-16 bg-white/10 rounded-full"
      />

      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <motion.h2
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-6xl md:text-8xl font-bold mb-6"
          >
            <span className="text-red-600">Kreatif</span>
            <br />
            <span className="text-white">Vizyonumuz</span>
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="text-xl text-gray-300 max-w-2xl mx-auto"
          >
            Her kare bir hikaye, her çekim bir sanat eseri. Yaratıcılığımızla sınırları aşıyoruz.
          </motion.p>
        </motion.div>

        {/* Creative Grid */}
        <div className="grid grid-cols-12 gap-6 h-[800px]">
          {/* Large Image */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="col-span-12 md:col-span-7 row-span-2 relative group overflow-hidden rounded-3xl"
          >
            <Image
              src={showcaseItems[0].image}
              alt={showcaseItems[0].title}
              fill
              className="object-cover transition-transform duration-700 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent" />
            <div className="absolute bottom-8 left-8 right-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
                className="inline-block px-4 py-2 bg-red-600 rounded-full text-sm font-medium mb-4"
              >
                {showcaseItems[0].category}
              </motion.div>
              <h3 className="text-3xl font-bold">{showcaseItems[0].title}</h3>
            </div>
            <motion.div
              className="absolute inset-0 bg-red-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            />
          </motion.div>

          {/* Medium Images */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="col-span-12 md:col-span-5 relative group overflow-hidden rounded-3xl"
          >
            <Image
              src={showcaseItems[1].image}
              alt={showcaseItems[1].title}
              fill
              className="object-cover transition-transform duration-700 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent" />
            <div className="absolute bottom-6 left-6 right-6">
              <div className="inline-block px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-xs font-medium mb-2">
                {showcaseItems[1].category}
              </div>
              <h3 className="text-xl font-bold">{showcaseItems[1].title}</h3>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="col-span-6 md:col-span-3 relative group overflow-hidden rounded-3xl"
          >
            <Image
              src={showcaseItems[2].image}
              alt={showcaseItems[2].title}
              fill
              className="object-cover transition-transform duration-700 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent" />
            <div className="absolute bottom-4 left-4 right-4">
              <div className="inline-block px-2 py-1 bg-white/20 backdrop-blur-sm rounded-full text-xs font-medium mb-2">
                {showcaseItems[2].category}
              </div>
              <h3 className="text-lg font-bold">{showcaseItems[2].title}</h3>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
            className="col-span-6 md:col-span-2 relative group overflow-hidden rounded-3xl"
          >
            <Image
              src={showcaseItems[3].image}
              alt={showcaseItems[3].title}
              fill
              className="object-cover transition-transform duration-700 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent" />
            <div className="absolute bottom-4 left-4 right-4">
              <div className="inline-block px-2 py-1 bg-white/20 backdrop-blur-sm rounded-full text-xs font-medium mb-2">
                {showcaseItems[3].category}
              </div>
              <h3 className="text-sm font-bold">{showcaseItems[3].title}</h3>
            </div>
          </motion.div>
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-20"
        >
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="group relative px-12 py-6 bg-gradient-to-r from-red-600 to-red-700 rounded-full text-xl font-bold overflow-hidden"
          >
            <span className="relative z-10">Portföyümüzü Keşfet</span>
            <motion.div
              className="absolute inset-0 bg-white"
              initial={{ scale: 0 }}
              whileHover={{ scale: 1 }}
              transition={{ duration: 0.3 }}
            />
            <span className="absolute inset-0 flex items-center justify-center text-red-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
              Portföyümüzü Keşfet
            </span>
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}

export default CreativeShowcase
