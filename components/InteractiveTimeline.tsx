'use client'

import { motion, useScroll, useTransform } from 'framer-motion'
import { useRef, useState } from 'react'

const InteractiveTimeline = () => {
  const containerRef = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  })

  const [activeStep, setActiveStep] = useState(0)

  const steps = [
    {
      id: 1,
      year: '2015',
      title: '<PERSON><PERSON>luş',
      description: 'VEE Studio, fotoğraf tutkusu olan genç bir ekip tarafından kuruldu.',
      icon: '🚀',
      color: 'from-red-500 to-red-600'
    },
    {
      id: 2,
      year: '2019',
      title: 'İlk Büyük Proje',
      description: 'Moda sektöründe ilk büyük kampanyamızı başarıyla tamamladık.',
      icon: '📸',
      color: 'from-red-600 to-red-700'
    },
    {
      id: 3,
      year: '2021',
      title: 'Stüdyo Genişletme',
      description: 'Modern ekipmanlarla donatılmış yeni stüdyomuzu açtık.',
      icon: '🏢',
      color: 'from-red-700 to-red-800'
    },
    {
      id: 4,
      year: '2023',
      title: 'Dijital Dönüşüm',
      description: 'AI destekli düzenleme teknolojilerini iş akışımıza entegre ettik.',
      icon: '🤖',
      color: 'from-red-800 to-red-900'
    },
    {
      id: 5,
      year: '2024',
      title: 'Bugün',
      description: '500+ mutlu müşteri, 1000+ proje ile sektörde lider konumdayız.',
      icon: '⭐',
      color: 'from-red-600 to-red-500'
    }
  ]

  const lineProgress = useTransform(scrollYProgress, [0, 1], [0, 100])

  return (
    <section ref={containerRef} className="py-32 bg-gradient-to-br from-gray-900 to-black text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_50%,rgba(220,38,38,0.1),transparent_50%)]"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <h2 className="text-5xl md:text-7xl font-bold mb-6">
            <span className="text-white">Bizim</span>
            <br />
            <span className="text-red-600">Hikayemiz</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Küçük bir hayalden başlayıp, bugün sektörün önde gelen isimlerinden biri haline geldik.
          </p>
        </motion.div>

        {/* Timeline */}
        <div className="relative max-w-6xl mx-auto">
          {/* Timeline Line */}
          <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gray-700 rounded-full">
            <motion.div
              style={{ height: `${lineProgress.get()}%` }}
              className="w-full bg-gradient-to-b from-red-500 to-red-700 rounded-full"
              initial={{ height: 0 }}
              animate={{ height: `${(activeStep + 1) * 20}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>

          {/* Timeline Steps */}
          <div className="space-y-24">
            {steps.map((step, index) => (
              <motion.div
                key={step.id}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                onViewportEnter={() => setActiveStep(index)}
                className={`flex items-center ${
                  index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'
                }`}
              >
                {/* Content */}
                <div className={`w-5/12 ${index % 2 === 0 ? 'text-right pr-8' : 'text-left pl-8'}`}>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700 hover:border-red-500/50 transition-all duration-300"
                  >
                    <motion.div
                      initial={{ scale: 0 }}
                      whileInView={{ scale: 1 }}
                      transition={{ duration: 0.5, delay: index * 0.1 + 0.3 }}
                      viewport={{ once: true }}
                      className={`inline-block px-4 py-2 bg-gradient-to-r ${step.color} rounded-full text-sm font-bold mb-4`}
                    >
                      {step.year}
                    </motion.div>
                    <h3 className="text-2xl font-bold mb-4 text-white">{step.title}</h3>
                    <p className="text-gray-300 leading-relaxed">{step.description}</p>
                  </motion.div>
                </div>

                {/* Timeline Node */}
                <div className="w-2/12 flex justify-center">
                  <motion.div
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    transition={{ duration: 0.5, delay: index * 0.1 + 0.2 }}
                    viewport={{ once: true }}
                    className="relative"
                  >
                    <motion.div
                      whileHover={{ scale: 1.2 }}
                      className={`w-16 h-16 bg-gradient-to-r ${step.color} rounded-full flex items-center justify-center text-2xl shadow-lg border-4 border-gray-900`}
                    >
                      {step.icon}
                    </motion.div>
                    <motion.div
                      className="absolute inset-0 bg-red-500 rounded-full opacity-30"
                      animate={{ scale: [1, 1.5, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    />
                  </motion.div>
                </div>

                {/* Empty Space */}
                <div className="w-5/12"></div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
          className="text-center mt-20"
        >
          <div className="bg-gradient-to-r from-red-600/20 to-red-700/20 backdrop-blur-sm rounded-3xl p-12 border border-red-500/30">
            <h3 className="text-3xl font-bold mb-6">Hikayenizin Bir Parçası Olmaya Hazır mısınız?</h3>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Bizimle çalışarak, kendi başarı hikayenizi yazmaya başlayın.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-red-600 to-red-700 text-white px-10 py-4 rounded-full text-lg font-semibold hover:shadow-2xl transition-all duration-300"
            >
              Hemen Başlayalım
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default InteractiveTimeline
